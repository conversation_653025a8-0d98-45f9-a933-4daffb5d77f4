import { Link } from "@tanstack/react-router";
import { Edit, Trash2 } from "lucide-react";
import type { Schedule } from "../../service/model/schedule";

interface Props {
	schedules: Schedule[];
}

export default function Table({ schedules }: Props) {
	const formatTime = (time: number) => {
		const hours = Math.floor(time / 100);
		const minutes = time % 100;
		return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`;
	};

	const formatDate = (dateString: string | null) => {
		if (!dateString) return "N/A";
		return new Date(dateString).toLocaleDateString();
	};

	return (
		<div className="overflow-x-auto">
			<table className="table-zebra table">
				<thead>
					<tr>
						<th>Nombre</th>
						<th>Duración Sesión</th>
						<th>Duración Descanso</th>
						<th>Turnos</th>
						<th>Fecha Creación</th>
						<th>Acciones</th>
					</tr>
				</thead>
				<tbody>
					{schedules.map((schedule) => (
						<tr key={schedule.id}>
							<td>
								<div className="font-bold">{schedule.name}</div>
							</td>
							<td>{schedule.sessionDuration} min</td>
							<td>{schedule.breakDuration} min</td>
							<td>
								<div className="space-y-1">
									{schedule.turns.map((turn) => (
										<div key={turn.id} className="badge badge-outline">
											{turn.name}: {formatTime(turn.startTime)} -{" "}
											{formatTime(turn.endTime)}
										</div>
									))}
								</div>
							</td>
							<td>{formatDate(schedule.createdAt)}</td>
							<td>
								<div className="flex gap-2">
									<Link
										to="/admin/schedules/edit/$id"
										params={{ id: schedule.id }}
										className="btn btn-sm btn-primary"
									>
										<Edit size={16} />
									</Link>
									<button
										type="button"
										className="btn btn-sm btn-error"
										onClick={() => {
											// TODO: Implement delete functionality
											console.log("Delete schedule:", schedule.id);
										}}
									>
										<Trash2 size={16} />
									</button>
								</div>
							</td>
						</tr>
					))}
				</tbody>
			</table>
		</div>
	);
}
